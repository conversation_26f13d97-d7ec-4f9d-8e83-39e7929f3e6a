body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f7f6;
    color: #333;
    line-height: 1.6;
}

header {
    background-color: #007bff;
    color: #ffffff;
    padding: 1.5rem 1rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

header h1 {
    margin: 0;
    font-size: 2rem;
}

main {
    padding: 2rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.search-container {
    margin-bottom: 2rem;
    text-align: center;
}

#searchInput {
    width: 80%;
    max-width: 600px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#searchInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.result-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
}

.result-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #0056b3;
    font-size: 1.25rem;
}

.result-card p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #555;
}

.result-card p strong {
    color: #333;
}

.result-card a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

.result-card a:hover {
    text-decoration: underline;
}

.result-card .category {
    font-style: italic;
    color: #777;
    font-size: 0.85rem;
    margin-bottom: 1rem;
}

.result-card .address {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.75rem;
}

footer {
    text-align: center;
    padding: 1.5rem 1rem;
    background-color: #343a40;
    color: #f8f9fa;
    margin-top: 2rem;
}

footer p {
    margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    header h1 {
        font-size: 1.75rem;
    }
    #searchInput {
        width: 90%;
    }
    .results-grid {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }
}