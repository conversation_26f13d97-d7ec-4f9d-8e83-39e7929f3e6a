document.addEventListener('DOMContentLoaded', function() {
    // Load and parse CSV data
    fetch('data.csv')
        .then(response => response.text())
        .then(data => {
            const parsedData = parseCSV(data);
            displayData(parsedData);
            setupSearch(parsedData);
            setupSorting(parsedData);
        })
        .catch(error => {
            console.error('Error loading data:', error);
            document.getElementById('data-container').innerHTML = 
                '<div class="error-message">Failed to load data. Please try again later.</div>';
        });
});

// Parse CSV data
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const headers = lines[0].split(',').map(header => header.trim());
    
    const data = [];
    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim() === '') continue;
        
        const values = lines[i].split(',').map(value => value.trim());
        const entry = {};
        
        headers.forEach((header, index) => {
            entry[header] = values[index];
        });
        
        data.push(entry);
    }
    
    return data;
}

// Display data in the grid
function displayData(data) {
    const container = document.getElementById('data-container');
    container.innerHTML = '';
    
    if (data.length === 0) {
        container.innerHTML = '<div class="no-results">No matching results found</div>';
        return;
    }
    
    data.forEach(item => {
        const dataItem = document.createElement('div');
        dataItem.className = 'data-item';
        
        let itemHTML = '<div class="data-content">';
        
        // Assuming the first column is the title
        const title = Object.values(item)[0];
        itemHTML += `<h3 class="data-title">${title}</h3>`;
        
        // Display other properties
        Object.entries(item).forEach(([key, value], index) => {
            if (index > 0) { // Skip the title which we already displayed
                itemHTML += `<p class="data-info"><strong>${key}:</strong> ${value}</p>`;
            }
        });
        
        itemHTML += '</div>';
        dataItem.innerHTML = itemHTML;
        container.appendChild(dataItem);
    });
}

// Setup search functionality
function setupSearch(data) {
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');
    
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        
        if (searchTerm === '') {
            displayData(data);
            return;
        }
        
        const filteredData = data.filter(item => {
            // Assuming the first column is the title
            const title = Object.values(item)[0].toLowerCase();
            return title.includes(searchTerm);
        });
        
        displayData(filteredData);
    }
    
    searchButton.addEventListener('click', performSearch);
    searchInput.addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            performSearch();
        }
    });
}

// Setup sorting functionality
function setupSorting(data) {
    const sortSelect = document.getElementById('sort-select');
    
    sortSelect.addEventListener('change', function() {
        const sortValue = this.value;
        let sortedData = [...data];
        
        if (sortValue === 'title-asc') {
            sortedData.sort((a, b) => {
                const titleA = Object.values(a)[0].toLowerCase();
                const titleB = Object.values(b)[0].toLowerCase();
                return titleA.localeCompare(titleB);
            });
        } else if (sortValue === 'title-desc') {
            sortedData.sort((a, b) => {
                const titleA = Object.values(a)[0].toLowerCase();
                const titleB = Object.values(b)[0].toLowerCase();
                return titleB.localeCompare(titleA);
            });
        }
        
        displayData(sortedData);
    });
}
