:root {
    --primary-color: #4a6bff;
    --secondary-color: #f5f7ff;
    --text-color: #333;
    --light-text: #777;
    --border-color: #eaeaea;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f9;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header */
header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 15px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover, nav ul li a.active {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    background-color: var(--secondary-color);
    padding: 60px 0;
    text-align: center;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    color: var(--primary-color);
}

.search-container {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
}

#search-input {
    flex: 1;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    font-size: 16px;
}

#search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 20px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

#search-button:hover {
    background-color: #3a5bef;
}

/* Filters Section */
.filters {
    padding: 20px 0;
    background-color: white;
    border-bottom: 1px solid var(--border-color);
}

.filter-options {
    display: flex;
    justify-content: flex-end;
}

select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
}

/* Data Display */
.data-display {
    padding: 40px 0;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.data-item {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.data-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.data-content {
    padding: 20px;
}

.data-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.data-info {
    color: var(--light-text);
    font-size: 14px;
    margin-bottom: 5px;
}

/* Footer */
footer {
    background-color: var(--text-color);
    color: white;
    padding: 30px 0;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    nav ul li {
        margin: 0 10px;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .data-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 480px) {
    .search-container {
        flex-direction: column;
    }
    
    #search-input, #search-button {
        width: 100%;
        border-radius: 4px;
    }
    
    #search-button {
        margin-top: 10px;
        padding: 10px;
    }
    
    .data-grid {
        grid-template-columns: 1fr;
    }
}